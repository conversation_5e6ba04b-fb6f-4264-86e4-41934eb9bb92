import React, { useState } from 'react';
import { useProcesosClientes } from '../../../hooks/useProcesosClientes';
import LoadingSpinner from '../../UI/LoadingSpinner';
import { PanelDetalleProcesoCliente } from '../procesos/PanelDetalleProcesoCliente';
import styles from './PestanaProcesosClientesEmpresa.module.css';
import type {
  ProcesoClienteFilters,
  ProcesoClienteGroupBy,
  VALOR_NEGOCIO_COLORS,
  PRIORIDAD_AUTOMATIZACION_COLORS,
} from '../../../types/proceso_cliente';

interface PestanaProcesosClientesEmpresaProps {
  empresaId: string;
}

export const PestanaProcesosClientesEmpresa: React.FC<PestanaProcesosClientesEmpresaProps> = ({
  empresaId,
}) => {
  const {
    procesos,
    contadores,
    procesoSeleccionado,
    tareas,
    personasDisponibles,
    departamentosDisponibles,
    isLoadingProcesos,
    isLoadingContadores,
    isLoadingProcesoDetalle,
    isLoadingTareas,
    errorProcesos,
    filters,
    groupBy,
    tareaFilters,
    setFilters,
    setGroupBy,
    setTareaFilters,
    clearFilters,
    seleccionarProceso,
    updateProceso,
    updateTarea,
    isPanelVisible,
    closePanel,
  } = useProcesosClientes({ empresaId });

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResponsables, setSelectedResponsables] = useState<string[]>([]);
  const [selectedDepartamento, setSelectedDepartamento] = useState<string>('');

  // Handle filter changes
  const handleFiltersChange = () => {
    const newFilters: ProcesoClienteFilters = {
      search: searchTerm || undefined,
      responsable_ids: selectedResponsables.length > 0 ? selectedResponsables : undefined,
      departamento_id: selectedDepartamento || undefined,
    };
    setFilters(newFilters);
  };



  // Apply filters when dependencies change
  React.useEffect(() => {
    const timeoutId = setTimeout(handleFiltersChange, 300);
    return () => clearTimeout(timeoutId);
  }, [searchTerm, selectedResponsables, selectedDepartamento]);



  const formatTiempo = (horas: number) => {
    if (horas === 0) return '0h';
    if (horas < 1) return `${Math.round(horas * 60)}min`;
    return `${horas.toFixed(1)}h`;
  };

  const getBadgeColor = (value: string, type: 'valor' | 'prioridad' | 'complejidad') => {
    switch (type) {
      case 'valor':
        // Map common values to colors
        if (value.toLowerCase().includes('alto')) return 'bg-red-100 text-red-800';
        if (value.toLowerCase().includes('medio')) return 'bg-yellow-100 text-yellow-800';
        if (value.toLowerCase().includes('bajo')) return 'bg-green-100 text-green-800';
        return 'bg-gray-100 text-gray-800';
      case 'prioridad':
        if (value.toLowerCase().includes('alta')) return 'bg-red-100 text-red-800';
        if (value.toLowerCase().includes('media')) return 'bg-yellow-100 text-yellow-800';
        if (value.toLowerCase().includes('baja')) return 'bg-green-100 text-green-800';
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoadingContadores || isLoadingProcesos) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (errorProcesos) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error al cargar los procesos: {errorProcesos.message}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header with counters - Compact */}
      <div className="bg-white border-b border-gray-200 p-2 flex-shrink-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <div className="bg-blue-50 rounded p-2">
            <h3 className="text-xs font-medium text-gray-700">Total Procesos</h3>
            <p className="text-lg font-bold text-blue-600">
              {contadores?.total_procesos || 0}
            </p>
          </div>
          <div className="bg-green-50 rounded p-2">
            <h3 className="text-xs font-medium text-gray-700">Total Tareas</h3>
            <p className="text-lg font-bold text-green-600">
              {contadores?.total_tareas || 0}
            </p>
          </div>
          <div className="bg-orange-50 rounded p-2">
            <h3 className="text-xs font-medium text-gray-700">Tiempo Manual Total</h3>
            <p className="text-lg font-bold text-orange-600">
              {formatTiempo(contadores?.tiempo_total_horas_mes || 0)}/mes
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main content area */}
        <div className={`flex flex-col ${isPanelVisible ? 'w-2/5' : 'w-full'} min-h-0`}>
          {/* Filters - Compact */}
          <div className="bg-white border-b border-gray-200 p-2 flex-shrink-0">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
              {/* Search */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Buscar
                </label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Nombre o descripción..."
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Responsables */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Responsables
                </label>
                <select
                  multiple
                  value={selectedResponsables}
                  onChange={(e) => setSelectedResponsables(Array.from(e.target.selectedOptions, option => option.value))}
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  size={2}
                >
                  {personasDisponibles.map((persona) => (
                    <option key={persona.id} value={persona.id}>
                      {persona.nombre} {persona.apellidos}
                    </option>
                  ))}
                </select>
              </div>

              {/* Departamento */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Departamento
                </label>
                <select
                  value={selectedDepartamento}
                  onChange={(e) => setSelectedDepartamento(e.target.value)}
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todos</option>
                  {departamentosDisponibles.map((dept) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.nombre}
                    </option>
                  ))}
                </select>
              </div>

              {/* Group by */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Agrupar por
                </label>
                <select
                  value={groupBy || ''}
                  onChange={(e) => setGroupBy(e.target.value as ProcesoClienteGroupBy || undefined)}
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Sin agrupar</option>
                  <option value="responsable_principal">Responsable Principal</option>
                  <option value="departamento">Departamento</option>
                  <option value="prioridad_automatizacion">Prioridad Automatización</option>
                </select>
              </div>
            </div>

            <div className="mt-2 flex justify-between items-center">
              <button
                onClick={clearFilters}
                className="px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
              >
                Limpiar filtros
              </button>
              <div className="text-xs text-gray-600">
                {procesos?.procesos.length || 0} de {procesos?.total || 0} procesos
                {procesos && procesos.tiempo_total_horas_mes > 0 && (
                  <span className="ml-2">
                    • {formatTiempo(procesos.tiempo_total_horas_mes)}/mes
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Process list with independent scroll */}
          <div className={`flex-1 overflow-y-auto overflow-x-hidden bg-white border-r border-gray-200 ${styles.procesosList}`}>
            {procesos?.procesos.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500 text-sm">No se encontraron procesos con los filtros aplicados</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {procesos?.procesos.map((proceso) => (
                  <div
                    key={proceso.id}
                    onClick={() => seleccionarProceso(proceso.id)}
                    className={`p-2 cursor-pointer ${styles.procesoItem} ${
                      procesoSeleccionado?.id === proceso.id ? styles.procesoSelected : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 text-sm truncate">{proceso.nombre}</h3>
                        {proceso.descripcion && (
                          <p className={`text-xs text-gray-600 mt-1 ${styles.lineClamp2}`}>{proceso.descripcion}</p>
                        )}
                        <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                          <span className="truncate">
                            👤 {proceso.responsable_principal?.nombre || 'Sin asignar'}
                          </span>
                          <span className="truncate">
                            🏢 {proceso.departamento?.nombre || 'Sin asignar'}
                          </span>
                          <span>
                            📋 {proceso.numero_tareas} tareas
                          </span>
                          <span>
                            ⏱️ {formatTiempo(proceso.tiempo_estimado_horas_mes)}/mes
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col gap-0.5 ml-1 flex-shrink-0">
                        {proceso.prioridad_automatizacion_aceleralia && (
                          <span className={`${styles.badge} ${getBadgeColor(proceso.prioridad_automatizacion_aceleralia, 'prioridad')}`}>
                            {proceso.prioridad_automatizacion_aceleralia}
                          </span>
                        )}
                        {proceso.valor_negocio_cliente && (
                          <span className={`${styles.badge} ${getBadgeColor(proceso.valor_negocio_cliente, 'valor')}`}>
                            {proceso.valor_negocio_cliente}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Detail panel */}
        {isPanelVisible && (
          <PanelDetalleProcesoCliente
            proceso={procesoSeleccionado}
            tareas={tareas}
            personasDisponibles={personasDisponibles}
            isLoadingProceso={isLoadingProcesoDetalle}
            isLoadingTareas={isLoadingTareas}
            tareaFilters={tareaFilters}
            onClose={closePanel}
            onUpdateProceso={updateProceso}
            onUpdateTarea={updateTarea}
            onTareaFiltersChange={setTareaFilters}
          />
        )}
      </div>
    </div>
  );
};
