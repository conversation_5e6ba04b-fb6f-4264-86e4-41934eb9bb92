import React, { useState, useRef, useEffect } from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface EditableFieldProps {
  label: string;
  value: string;
  type: 'text' | 'textarea' | 'number' | 'select';
  options?: SelectOption[];
  onSave: (value: string) => Promise<void> | void;
  className?: string;
  renderValue?: (value: string) => React.ReactNode;
  placeholder?: string;
  disabled?: boolean;
}

export const EditableField: React.FC<EditableFieldProps> = ({
  label,
  value,
  type,
  options = [],
  onSave,
  className = '',
  renderValue,
  placeholder,
  disabled = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>(null);

  // Update edit value when prop value changes
  useEffect(() => {
    if (!isEditing) {
      setEditValue(value);
    }
  }, [value, isEditing]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (type === 'text' || type === 'textarea') {
        (inputRef.current as HTMLInputElement | HTMLTextAreaElement).select();
      }
    }
  }, [isEditing, type]);

  const handleStartEdit = () => {
    if (disabled) return;
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value);
    setError(null);
  };

  const handleSave = async () => {
    if (editValue === value) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      await onSave(editValue);
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al guardar');
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && type !== 'textarea') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const renderInput = () => {
    const commonProps = {
      ref: inputRef as any,
      value: editValue,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => 
        setEditValue(e.target.value),
      onKeyDown: handleKeyDown,
      className: "w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500",
      placeholder,
    };

    switch (type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows={3}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                handleSave();
              } else if (e.key === 'Escape') {
                e.preventDefault();
                handleCancel();
              }
            }}
          />
        );
      case 'number':
        return (
          <input
            {...commonProps}
            type="number"
            min="0"
          />
        );
      case 'select':
        return (
          <select {...commonProps}>
            <option value="">Seleccionar...</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      default:
        return <input {...commonProps} type="text" />;
    }
  };

  const renderDisplayValue = () => {
    if (renderValue) {
      return renderValue(value);
    }

    if (!value || value.trim() === '') {
      return <span className="text-gray-400 italic">{placeholder || 'Sin valor'}</span>;
    }

    return <span>{value}</span>;
  };

  if (isEditing) {
    return (
      <div className={`space-y-2 ${className}`}>
        <label className="block text-xs font-medium text-gray-700">
          {label}
        </label>
        <div className="space-y-2">
          {renderInput()}
          {error && (
            <p className="text-xs text-red-600">{error}</p>
          )}
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {isSaving ? 'Guardando...' : 'Guardar'}
            </button>
            <button
              onClick={handleCancel}
              disabled={isSaving}
              className="px-2 py-1 text-xs bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
            >
              Cancelar
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`group ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <label className="block text-xs font-medium text-gray-700 mb-1">
            {label}
          </label>
          <div
            onClick={handleStartEdit}
            className={`cursor-pointer p-2 rounded border border-transparent hover:border-gray-300 hover:bg-gray-50 transition-colors ${
              disabled ? 'cursor-not-allowed opacity-50' : ''
            }`}
          >
            {renderDisplayValue()}
          </div>
        </div>
        {!disabled && (
          <button
            onClick={handleStartEdit}
            className="opacity-0 group-hover:opacity-100 ml-2 p-1 text-gray-400 hover:text-gray-600 transition-opacity"
            title={`Editar ${label}`}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};
