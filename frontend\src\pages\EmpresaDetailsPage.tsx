/**
 * EmpresaDetailsPage - Vista de detalle de empresa con sistema de pestañas
 * Módulo 1.2: Implementa la arquitectura base para la vista de detalle
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useEmpresas } from '../hooks/useEmpresas';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import PestanaGeneralEmpresa from '../components/empresas/pestanas/PestanaGeneralEmpresa';
import PestanaHallazgosEmpresa from '../components/empresas/pestanas/PestanaHallazgosEmpresa';
import PestanaReunionesEmpresa from '../components/empresas/pestanas/PestanaReunionesEmpresa';
import { PestanaProcesosClientesEmpresa } from '../components/empresas/pestanas/PestanaProcesosClientesEmpresa';
import type { EmpresaDetailTab, EmpresaDetailTabConfig } from '../types/empresa';

// Tab components
const GeneralTab: React.FC<{ empresaId: string }> = ({ empresaId }) => (
  <PestanaGeneralEmpresa empresaId={empresaId} />
);

const ReunionesTab: React.FC<{ empresaId: string }> = ({ empresaId }) => (
  <PestanaReunionesEmpresa empresaId={empresaId} />
);

const HallazgosTab: React.FC<{ empresaId: string }> = ({ empresaId }) => (
  <PestanaHallazgosEmpresa empresaId={empresaId} />
);

const ProcesosClientesTab: React.FC<{ empresaId: string }> = ({ empresaId }) => (
  <PestanaProcesosClientesEmpresa empresaId={empresaId} />
);

// Tab configuration
const tabsConfig: EmpresaDetailTabConfig[] = [
  {
    id: 'general',
    label: 'General',
    icon: '📋',
    component: GeneralTab,
  },
  {
    id: 'reuniones',
    label: 'Reuniones',
    icon: '🎥',
    component: ReunionesTab,
  },
  {
    id: 'hallazgos',
    label: 'Hallazgos',
    icon: '🔍',
    component: HallazgosTab,
  },
  {
    id: 'procesos_clientes',
    label: 'Procesos de Clientes',
    icon: '⚙️',
    component: ProcesosClientesTab,
  },
];

const EmpresaDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<EmpresaDetailTab>('general');
  
  const {
    empresa,
    loading,
    error,
    fetchEmpresaById,
    clearError,
    clearEmpresa
  } = useEmpresas({ autoFetch: false });
  
  // Fetch empresa on mount
  useEffect(() => {
    if (id) {
      fetchEmpresaById(id);
    }
    
    // Cleanup on unmount
    return () => {
      clearEmpresa();
    };
  }, [id, fetchEmpresaById, clearEmpresa]);
  
  // Handle back navigation
  const handleBack = () => {
    navigate('/crm/empresas');
  };
  
  // Handle tab change
  const handleTabChange = (tabId: EmpresaDetailTab) => {
    setActiveTab(tabId);
  };
  
  // Get active tab component
  const getActiveTabComponent = () => {
    const activeTabConfig = tabsConfig.find(tab => tab.id === activeTab);
    if (!activeTabConfig || !activeTabConfig.component || !id) {
      return null;
    }
    
    const TabComponent = activeTabConfig.component;
    return <TabComponent empresaId={id} />;
  };
  
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error al cargar la empresa</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="inline-flex text-red-400 hover:text-red-600"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <div className="mt-6 text-center">
          <button
            onClick={handleBack}
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Volver a Empresas
          </button>
        </div>
      </div>
    );
  }
  
  // Not found state
  if (!empresa) {
    return (
      <div className="max-w-4xl mx-auto p-6 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Empresa no encontrada</h2>
        <p className="text-gray-600 mb-6">
          La empresa que buscas no existe o no tienes permisos para verla.
        </p>
        <button
          onClick={handleBack}
          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Volver a Empresas
        </button>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBack}
              className="inline-flex items-center text-gray-500 hover:text-gray-700 transition-colors"
            >
              <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Volver
            </button>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{empresa.nombre}</h1>
              <div className="flex items-center space-x-4 mt-1">
                {empresa.tipo_relacion && (
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {empresa.tipo_relacion}
                  </span>
                )}
                {empresa.sector && (
                  <span className="text-sm text-gray-600">{empresa.sector}</span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Editar
            </button>
          </div>
        </div>
      </div>
      
      {/* Tabs Navigation */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            {tabsConfig.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
        
        {/* Tab Content - Full width for optimal space usage */}
        <div className={activeTab === 'procesos-clientes' ? 'p-0' : 'p-6'}>
          {getActiveTabComponent()}
        </div>
      </div>
    </div>
  );
};

export default EmpresaDetailsPage;
