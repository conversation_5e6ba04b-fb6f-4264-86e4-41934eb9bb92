from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List # Import List
from uuid import UUID

from app.models.crm_empresa import (
    EmpresaCreate,
    Empresa as EmpresaResponse,
    EmpresaGeneralDetails,
    EmpresaHallazgosDetails,
    HallazgoDetail,
    EmpresaReunionesDetails
)
from app.services import crm_empresa_service
from app.core.security import get_current_user_id
# from app.core.database import get_db

router = APIRouter()

@router.post("", response_model=EmpresaResponse, status_code=status.HTTP_201_CREATED) # Removed trailing slash from path
async def create_new_empresa(
    empresa_in: EmpresaCreate,
    # db: Session = Depends(get_db), # If using SQLAlchemy sessions
    current_user_id: UUID = Depends(get_current_user_id) # Get current user
):
    """
    Create a new empresa.
    """
    try:
        # db=None no longer needed as service function signature changed
        created_empresa_data = await crm_empresa_service.create_empresa(empresa_in=empresa_in, user_id=current_user_id)
        
        # The service returns a dict, we need to ensure it matches the EmpresaResponse model.
        # If create_empresa returns the full DB object including id, created_at, etc.
        # Pydantic will handle the conversion if the keys match.
        # For now, we assume the dict returned by the service is compatible.
        # A more robust approach would be to fetch the created record from DB using its ID
        # and then return it, or ensure the service returns a Pydantic model instance.
        
        # Let's refine this: The service returns a dict. We should map it to the response model.
        # This assumes the service returns all necessary fields for EmpresaResponse.
        # If `fecha_alta` is auto-set by DB or service, it should be in `created_empresa_data`.
        # `created_at` and `updated_at` are also expected by `EmpresaResponse`.
        # The current service returns the direct Supabase response.data[0] which should be a dict.
        
        # Validate that the returned data can be parsed into EmpresaResponse
        # This might require fetching the full object if Supabase insert doesn't return everything
        # or if the structure is slightly different.
        # For simplicity, assuming direct compatibility for now.
        # If issues arise, fetch the record by ID after creation.
        return EmpresaResponse(**created_empresa_data)

    except Exception as e:
        # Log the exception e
        # Consider specific exception types for more granular error handling
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create empresa: {str(e)}"
        )

@router.get("", response_model=List[EmpresaResponse]) # Removed trailing slash from path
async def list_empresas(
    search: str | None = Query(None, alias="search"), # Use Query for explicit query param
    skip: int = 0,
    limit: int = 100,
    current_user_id: UUID = Depends(get_current_user_id) # Ensure endpoint is protected
):
    """
    Retrieve a list of empresas, with optional search.
    """
    try:
        empresas_data = await crm_empresa_service.get_empresas(search=search, skip=skip, limit=limit)
        # The service now returns a list of Pydantic model instances (or raises an error)
        return empresas_data
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve empresas: {str(e)}"
        )

@router.get("/{empresa_id}", response_model=EmpresaResponse)
async def get_empresa_by_id(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve a specific empresa by ID.
    """
    try:
        empresa_data = await crm_empresa_service.get_empresa_by_id(empresa_id=empresa_id)
        if not empresa_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Empresa with ID {empresa_id} not found"
            )
        return EmpresaResponse(**empresa_data)
    except HTTPException:
        # Re-raise HTTP exceptions (like 404)
        raise
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve empresa: {str(e)}"
        )

@router.get("/{empresa_id}/general-details", response_model=EmpresaGeneralDetails)
async def get_empresa_general_details(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve comprehensive general details for a specific empresa including:
    - Basic empresa info
    - Departments
    - Related projects with progress
    - Active workers count
    - Meeting/interview statistics
    - Client processes and tasks statistics
    - Findings distribution
    - People panel data
    """
    try:
        general_details = await crm_empresa_service.get_empresa_general_details(empresa_id=empresa_id)
        return general_details
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve empresa general details: {str(e)}"
        )

@router.get("/{empresa_id}/hallazgos-details", response_model=EmpresaHallazgosDetails)
async def get_empresa_hallazgos_details(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve comprehensive hallazgos details for a specific empresa including:
    - List of all findings with basic information
    - Statistics and distribution by type
    - Available filter options (personas, departamentos, tipos, impactos)
    """
    try:
        hallazgos_details = await crm_empresa_service.get_empresa_hallazgos_details(empresa_id=empresa_id)
        return hallazgos_details
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve empresa hallazgos details: {str(e)}"
        )

@router.get("/hallazgos/{hallazgo_id}", response_model=HallazgoDetail)
async def get_hallazgo_detail(
    hallazgo_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve detailed information for a specific hallazgo including related processes.
    """
    try:
        hallazgo_detail = await crm_empresa_service.get_hallazgo_detail(hallazgo_id=hallazgo_id)
        return hallazgo_detail
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve hallazgo detail: {str(e)}"
        )

@router.get("/{empresa_id}/reuniones-details", response_model=EmpresaReunionesDetails)
async def get_empresa_reuniones_details(
    empresa_id: UUID,
    current_user_id: UUID = Depends(get_current_user_id)
):
    """
    Retrieve comprehensive reuniones details for a specific empresa including:
    - List of meetings/interviews associated with the company
    - Company people with interview status
    - Timeline of meetings
    - Statistics (total meetings vs interviews, people interviewed)
    """
    try:
        reuniones_details = await crm_empresa_service.get_empresa_reuniones_details(empresa_id=empresa_id)
        return reuniones_details
    except Exception as e:
        # Log the exception e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve empresa reuniones details: {str(e)}"
        )

# Add other endpoints like update, delete as needed.
