/**
 * PestanaHallazgosEmpresa - Hallazgos tab component for company details
 * Shows comprehensive findings information with filtering, sorting, and details modal
 */

import React, { useState, useEffect, useMemo } from 'react';
import { apiClient } from '../../../lib/api';
import LoadingSpinner from '../../UI/LoadingSpinner';
import Modal from '../../UI/Modal';
import type {
  EmpresaHallazgosDetails,
  HallazgoListItem,
  HallazgoDetail,
  HallazgoFilterState,
  HallazgoSortState,
  HallazgoSortField
} from '../../../types/empresa';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Eye,
  AlertCircle,
  BarChart3,
  Users,
  Building2,
  Calendar,
  FileText,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface PestanaHallazgosEmpresaProps {
  empresaId: string;
}

// Color mapping for finding types
const TIPO_COLORS: Record<string, string> = {
  'ineficiencia': 'bg-red-100 text-red-800 border-red-200',
  'ladron_tiempo': 'bg-orange-100 text-orange-800 border-orange-200',
  'oportunidad_mejora': 'bg-green-100 text-green-800 border-green-200',
  'riesgo_identificado': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'deficit_gobernanza_datos': 'bg-purple-100 text-purple-800 border-purple-200',
  'falta_estandarizacion': 'bg-blue-100 text-blue-800 border-blue-200',
  'equipamiento_inadecuado': 'bg-gray-100 text-gray-800 border-gray-200',
};

const PestanaHallazgosEmpresa: React.FC<PestanaHallazgosEmpresaProps> = ({ empresaId }) => {
  const [data, setData] = useState<EmpresaHallazgosDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  // Filter and sort state
  const [filters, setFilters] = useState<HallazgoFilterState>({});
  const [sort, setSort] = useState<HallazgoSortState>({
    field: 'created_at',
    direction: 'desc'
  });
  
  // Detail modal state
  const [selectedHallazgo, setSelectedHallazgo] = useState<HallazgoDetail | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [loadingDetail, setLoadingDetail] = useState(false);

  useEffect(() => {
    const fetchHallazgosDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.empresas.getHallazgosDetails(empresaId);
        setData(response);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Error loading hallazgos details';
        setError(errorMessage);
        console.error('Error fetching empresa hallazgos details:', err);
      } finally {
        setLoading(false);
      }
    };

    if (empresaId) {
      fetchHallazgosDetails();
    }
  }, [empresaId]);

  // Filter and sort hallazgos
  const filteredAndSortedHallazgos = useMemo(() => {
    if (!data?.hallazgos) return [];

    let filtered = data.hallazgos.filter(hallazgo => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch =
          (hallazgo.titulo && hallazgo.titulo.toLowerCase().includes(searchLower)) ||
          (hallazgo.tipo && hallazgo.tipo.toLowerCase().includes(searchLower)) ||
          (hallazgo.impacto && hallazgo.impacto.toLowerCase().includes(searchLower)) ||
          (hallazgo.departamento_nombre && hallazgo.departamento_nombre.toLowerCase().includes(searchLower)) ||
          (hallazgo.persona_nombre && hallazgo.persona_nombre.toLowerCase().includes(searchLower));

        if (!matchesSearch) return false;
      }

      // Other filters
      if (filters.tipo && hallazgo.tipo !== filters.tipo) return false;
      if (filters.impacto && hallazgo.impacto !== filters.impacto) return false;
      
      return true;
    });

    // Sort without mutating the original array
    const sorted = [...filtered].sort((a, b) => {
      let aValue: string | number = '';
      let bValue: string | number = '';

      switch (sort.field) {
        case 'titulo':
          aValue = a.titulo || '';
          bValue = b.titulo || '';
          break;
        case 'tipo':
          aValue = a.tipo || '';
          bValue = b.tipo || '';
          break;
        case 'impacto':
          aValue = a.impacto || '';
          bValue = b.impacto || '';
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sort.direction === 'asc' ? comparison : -comparison;
      } else {
        const comparison = (aValue as number) - (bValue as number);
        return sort.direction === 'asc' ? comparison : -comparison;
      }
    });

    return sorted;
  }, [data?.hallazgos, filters, sort]);

  const handleSortChange = (field: HallazgoSortField) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleViewDetail = async (hallazgo: HallazgoListItem) => {
    try {
      setLoadingDetail(true);
      const detail = await apiClient.empresas.getHallazgoDetail(hallazgo.id);
      setSelectedHallazgo(detail);
      setShowDetailModal(true);
    } catch (err) {
      console.error('Error fetching hallazgo detail:', err);
      // Show basic info if detail fetch fails
      setSelectedHallazgo({
        id: hallazgo.id,
        titulo: hallazgo.titulo || 'Sin título',
        tipo: hallazgo.tipo || 'Sin clasificar',
        impacto: hallazgo.impacto || 'Sin especificar',
        departamento_nombre: hallazgo.departamento_nombre,
        persona_nombre: hallazgo.persona_nombre,
        estado: hallazgo.estado,
        procesos_relacionados: [],
        created_at: hallazgo.created_at,
        updated_at: hallazgo.created_at
      });
      setShowDetailModal(true);
    } finally {
      setLoadingDetail(false);
    }
  };

  const clearFilters = () => {
    setFilters({});
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error al cargar los datos</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No se encontraron datos de hallazgos para esta empresa.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Summary Stats */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <BarChart3 className="h-6 w-6 text-blue-600 mr-2" />
            Hallazgos Identificados
          </h2>
          <div className="text-2xl font-bold text-blue-600">
            {data.total_hallazgos}
          </div>
        </div>
        
        {/* Type Distribution */}
        {data.distribucion_tipos.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {data.distribucion_tipos.slice(0, 4).map((tipo) => (
              <div key={tipo.tipo} className="text-center">
                <div className="text-lg font-semibold text-gray-900">{tipo.count}</div>
                <div className="text-xs text-gray-600 capitalize">
                  {tipo.tipo.replace('_', ' ')}
                </div>
                <div className="text-xs text-gray-500">
                  {tipo.percentage.toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Filters and Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar hallazgos..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Sort */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Ordenar por:</span>
              <select
                value={sort.field}
                onChange={(e) => handleSortChange(e.target.value as HallazgoSortField)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="created_at">Fecha</option>
                <option value="titulo">Título</option>
                <option value="tipo">Tipo</option>
                <option value="impacto">Impacto</option>
              </select>
              <button
                onClick={() => setSort(prev => ({ ...prev, direction: prev.direction === 'asc' ? 'desc' : 'asc' }))}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                {sort.direction === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="h-4 w-4" />
              Filtros
              {showFilters ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </button>
            
            {Object.keys(filters).length > 0 && (
              <button
                onClick={clearFilters}
                className="px-3 py-2 text-sm text-red-600 hover:text-red-800"
              >
                Limpiar
              </button>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
                <select
                  value={filters.tipo || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, tipo: e.target.value || undefined }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Todos los tipos</option>
                  {data.filtros_disponibles.tipos_disponibles.map(tipo => (
                    <option key={tipo} value={tipo}>
                      {tipo.replace('_', ' ').charAt(0).toUpperCase() + tipo.replace('_', ' ').slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Impact Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Impacto</label>
                <select
                  value={filters.impacto || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, impacto: e.target.value || undefined }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Todos los impactos</option>
                  {data.filtros_disponibles.impactos_disponibles.map(impacto => (
                    <option key={impacto} value={impacto}>{impacto}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Hallazgos Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Lista de Hallazgos ({filteredAndSortedHallazgos.length})
          </h3>
        </div>

        {filteredAndSortedHallazgos.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              {data.total_hallazgos === 0
                ? 'No se han identificado hallazgos para esta empresa.'
                : 'No se encontraron hallazgos que coincidan con los filtros aplicados.'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Título
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Impacto
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Departamento
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Persona
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fecha
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAndSortedHallazgos.map((hallazgo) => (
                  <tr key={hallazgo.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {hallazgo.titulo || 'Sin título'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${
                        TIPO_COLORS[hallazgo.tipo || ''] || 'bg-gray-100 text-gray-800 border-gray-200'
                      }`}>
                        {hallazgo.tipo ?
                          hallazgo.tipo.replace('_', ' ').charAt(0).toUpperCase() + hallazgo.tipo.replace('_', ' ').slice(1) :
                          'Sin clasificar'
                        }
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {hallazgo.impacto || 'Sin especificar'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {hallazgo.departamento_nombre || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {hallazgo.persona_nombre || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(hallazgo.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleViewDetail(hallazgo)}
                        disabled={loadingDetail}
                        className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedHallazgo && (
        <Modal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          title="Detalle del Hallazgo"
          size="lg"
        >
          <div className="space-y-6">
            {/* Header */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {selectedHallazgo.titulo || 'Sin título'}
              </h3>
              <div className="flex items-center gap-4">
                <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full border ${
                  TIPO_COLORS[selectedHallazgo.tipo || ''] || 'bg-gray-100 text-gray-800 border-gray-200'
                }`}>
                  {selectedHallazgo.tipo ?
                    selectedHallazgo.tipo.replace('_', ' ').charAt(0).toUpperCase() + selectedHallazgo.tipo.replace('_', ' ').slice(1) :
                    'Sin clasificar'
                  }
                </span>
                {selectedHallazgo.estado && (
                  <span className="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                    {selectedHallazgo.estado}
                  </span>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Impacto</label>
                  <p className="text-sm text-gray-900">{selectedHallazgo.impacto || 'Sin especificar'}</p>
                </div>

                {selectedHallazgo.descripcion && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Descripción</label>
                    <p className="text-sm text-gray-900">{selectedHallazgo.descripcion}</p>
                  </div>
                )}

                {selectedHallazgo.posible_solucion && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Posible Solución</label>
                    <p className="text-sm text-gray-900">{selectedHallazgo.posible_solucion}</p>
                  </div>
                )}
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                {selectedHallazgo.departamento_nombre && (
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <label className="block text-xs font-medium text-gray-500">Departamento</label>
                      <p className="text-sm text-gray-900">{selectedHallazgo.departamento_nombre}</p>
                    </div>
                  </div>
                )}

                {selectedHallazgo.persona_nombre && (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <label className="block text-xs font-medium text-gray-500">Persona</label>
                      <p className="text-sm text-gray-900">{selectedHallazgo.persona_nombre}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                  <div>
                    <label className="block text-xs font-medium text-gray-500">Fecha de Identificación</label>
                    <p className="text-sm text-gray-900">
                      {new Date(selectedHallazgo.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Processes */}
            {selectedHallazgo.procesos_relacionados.length > 0 && (
              <div className="border-t border-gray-200 pt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Procesos Relacionados
                </label>
                <div className="space-y-2">
                  {selectedHallazgo.procesos_relacionados.map((proceso, index) => (
                    <div key={index} className="flex items-center p-2 bg-gray-50 rounded-md">
                      <FileText className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">{proceso}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="flex justify-end pt-4 border-t border-gray-200">
              <button
                onClick={() => setShowDetailModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cerrar
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default PestanaHallazgosEmpresa;
