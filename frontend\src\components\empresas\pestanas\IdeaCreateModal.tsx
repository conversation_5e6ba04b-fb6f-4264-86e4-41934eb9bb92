/**
 * IdeaCreateModal - Modal component for creating new ideas
 */

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Modal from '../../UI/Modal';
import LoadingSpinner from '../../UI/LoadingSpinner';
import { apiClient } from '../../../lib/api';
import type {
  IdeaCreate,
  EstadoIdea,
  PrioridadIdea,
} from '../../../types/idea';
import {
  ESTADOS_IDEA,
  PRIORIDADES_IDEA,
} from '../../../types/idea';
import {
  Lightbulb,
  X,
  AlertCircle,
} from 'lucide-react';

// Validation schema
const ideaSchema = z.object({
  titulo: z.string()
    .min(1, 'El título es requerido')
    .max(255, 'El título no puede exceder 255 caracteres'),
  descripcion: z.string()
    .max(1000, 'La descripción no puede exceder 1000 caracteres')
    .optional(),
  estado: z.enum(['pendiente', 'implementada', 'descartada'] as const),
  prioridad: z.enum(['Baja', 'Media', 'Alta', 'Urgente'] as const),
  proyecto_relacionado_id: z.string().optional(),
});

type IdeaFormValues = z.infer<typeof ideaSchema>;

interface IdeaCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  empresaId: string;
  empresaNombre: string;
  onIdeaCreated: (idea: any) => void;
}

interface ProyectoOption {
  id: string;
  nombre: string;
}

const IdeaCreateModal: React.FC<IdeaCreateModalProps> = ({
  isOpen,
  onClose,
  empresaId,
  empresaNombre,
  onIdeaCreated,
}) => {
  const [loading, setLoading] = useState(false);
  const [proyectos, setProyectos] = useState<ProyectoOption[]>([]);
  const [loadingProyectos, setLoadingProyectos] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid },
    watch,
  } = useForm<IdeaFormValues>({
    resolver: zodResolver(ideaSchema),
    defaultValues: {
      titulo: '',
      descripcion: '',
      estado: 'pendiente',
      prioridad: 'Media',
      proyecto_relacionado_id: '',
    },
    mode: 'onChange',
  });

  // Load proyectos when modal opens
  useEffect(() => {
    if (isOpen) {
      loadProyectos();
    }
  }, [isOpen]);

  const loadProyectos = async () => {
    try {
      setLoadingProyectos(true);
      // Get proyectos associated with this empresa
      const response = await apiClient.tareas.getProyectos();
      setProyectos(response || []);
    } catch (error) {
      console.error('Error loading proyectos:', error);
      setProyectos([]);
    } finally {
      setLoadingProyectos(false);
    }
  };

  const onSubmit = async (data: IdeaFormValues) => {
    try {
      setLoading(true);

      const ideaData: IdeaCreate = {
        titulo: data.titulo,
        descripcion: data.descripcion || undefined,
        empresa_relacionada_id: empresaId,
        proyecto_relacionado_id: data.proyecto_relacionado_id || undefined,
        estado: data.estado,
        prioridad: data.prioridad,
      };

      const newIdea = await apiClient.ideas.create(ideaData);
      
      // Notify parent component
      onIdeaCreated(newIdea);
      
      // Reset form and close modal
      reset();
      onClose();
    } catch (error) {
      console.error('Error creating idea:', error);
      // Error handling could be improved with toast notifications
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      reset();
      onClose();
    }
  };

  const watchedTitulo = watch('titulo');
  const watchedDescripcion = watch('descripcion');

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Nueva Idea"
      size="lg"
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3 pb-4 border-b border-gray-200">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Lightbulb className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Crear Nueva Idea
            </h3>
            <p className="text-sm text-gray-500">
              Empresa: <span className="font-medium">{empresaNombre}</span>
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Título */}
          <div>
            <label htmlFor="titulo" className="block text-sm font-medium text-gray-700 mb-2">
              Título de la Idea *
            </label>
            <input
              type="text"
              id="titulo"
              {...register('titulo')}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.titulo ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Ej: Implementar sistema de automatización..."
              disabled={loading}
            />
            {errors.titulo && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.titulo.message}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {watchedTitulo?.length || 0}/255 caracteres
            </p>
          </div>

          {/* Descripción */}
          <div>
            <label htmlFor="descripcion" className="block text-sm font-medium text-gray-700 mb-2">
              Descripción
            </label>
            <textarea
              id="descripcion"
              {...register('descripcion')}
              rows={4}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.descripcion ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describe la idea en detalle, incluyendo objetivos, beneficios esperados y consideraciones importantes..."
              disabled={loading}
            />
            {errors.descripcion && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.descripcion.message}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {watchedDescripcion?.length || 0}/1000 caracteres
            </p>
          </div>

          {/* Estado y Prioridad */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Estado */}
            <div>
              <label htmlFor="estado" className="block text-sm font-medium text-gray-700 mb-2">
                Estado *
              </label>
              <select
                id="estado"
                {...register('estado')}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.estado ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={loading}
              >
                {ESTADOS_IDEA.map(estado => (
                  <option key={estado} value={estado}>
                    {estado.charAt(0).toUpperCase() + estado.slice(1)}
                  </option>
                ))}
              </select>
              {errors.estado && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.estado.message}
                </p>
              )}
            </div>

            {/* Prioridad */}
            <div>
              <label htmlFor="prioridad" className="block text-sm font-medium text-gray-700 mb-2">
                Prioridad *
              </label>
              <select
                id="prioridad"
                {...register('prioridad')}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.prioridad ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={loading}
              >
                {PRIORIDADES_IDEA.map(prioridad => (
                  <option key={prioridad} value={prioridad}>
                    {prioridad}
                  </option>
                ))}
              </select>
              {errors.prioridad && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.prioridad.message}
                </p>
              )}
            </div>
          </div>

          {/* Proyecto Relacionado */}
          <div>
            <label htmlFor="proyecto_relacionado_id" className="block text-sm font-medium text-gray-700 mb-2">
              Proyecto Relacionado (Opcional)
            </label>
            {loadingProyectos ? (
              <div className="flex items-center justify-center py-3">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-sm text-gray-500">Cargando proyectos...</span>
              </div>
            ) : (
              <select
                id="proyecto_relacionado_id"
                {...register('proyecto_relacionado_id')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                <option value="">Seleccionar proyecto (opcional)</option>
                {proyectos.map(proyecto => (
                  <option key={proyecto.id} value={proyecto.id}>
                    {proyecto.nombre}
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading || !isValid}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span className="ml-2">Creando...</span>
                </>
              ) : (
                <>
                  <Lightbulb className="h-4 w-4 mr-2" />
                  Crear Idea
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default IdeaCreateModal;
